[gd_scene load_steps=16 format=3 uid="uid://dfwqb2pxuyn2x"]

[ext_resource type="Script" uid="uid://cpwlujhr03k32" path="res://scripts/main.gd" id="1_main"]
[ext_resource type="Script" uid="uid://ca6mrkxgqkq8m" path="res://scripts/player_controller.gd" id="2_player"]
[ext_resource type="Script" uid="uid://ci8as5fldyonr" path="res://scripts/camera_controller.gd" id="3_camera"]
[ext_resource type="Script" uid="uid://duopyyl4f35fa" path="res://scripts/level_environment.gd" id="4_environment"]
[ext_resource type="PackedScene" uid="uid://c2mgc2jeoj357" path="res://assets/models/skater/skater.glb" id="5_skater"]
[ext_resource type="PackedScene" uid="uid://b8xqr7y8qn2vp" path="res://scenes/skateboard.tscn" id="6_skateboard"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_1"]
radius = 0.3
height = 1.8

[sub_resource type="BoxShape3D" id="BoxShape3D_feet"]
size = Vector3(0.4, 0.1, 0.2)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_1"]
albedo_color = Color(0.5, 0.5, 0.5, 1)
roughness = 0.8

[sub_resource type="BoxMesh" id="BoxMesh_1"]
size = Vector3(100, 0.1, 100)

[sub_resource type="BoxShape3D" id="BoxShape3D_1"]
size = Vector3(100, 0.1, 100)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_4"]
albedo_color = Color(1, 0, 0, 1)

[sub_resource type="BoxMesh" id="BoxMesh_3"]

[sub_resource type="BoxShape3D" id="BoxShape3D_2"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_5"]
albedo_color = Color(0, 1, 0, 1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_6"]
albedo_color = Color(0, 0, 1, 1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_7"]
albedo_color = Color(1, 1, 0, 1)

[node name="Main" type="Node3D"]
script = ExtResource("1_main")

[node name="Player" type="CharacterBody3D" parent="."]
transform = Transform3D(-1, 0, 8.742278e-08, 0, 1, 0, -8.742278e-08, 0, -1, 0, 1, 0)
script = ExtResource("2_player")
skateboard_scene = ExtResource("6_skateboard")

[node name="MeshInstance3D" type="MeshInstance3D" parent="Player"]

[node name="SkaterModel" parent="Player/MeshInstance3D" instance=ExtResource("5_skater")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="Player"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.100000024, 0)
shape = SubResource("CapsuleShape3D_1")

[node name="SkateboardPickupArea" type="Area3D" parent="Player"]

[node name="CollisionShape3D" type="CollisionShape3D" parent="Player/SkateboardPickupArea"]

[node name="DeckDetectionArea" type="Area3D" parent="Player"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.9, 0)

[node name="CollisionShape3D" type="CollisionShape3D" parent="Player/DeckDetectionArea"]
shape = SubResource("BoxShape3D_feet")

[node name="CameraController" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2, 5)
script = ExtResource("3_camera")

[node name="Camera3D" type="Camera3D" parent="CameraController"]
transform = Transform3D(1, 0, 0, 0, 0.866025, 0.5, 0, -0.5, 0.866025, 0, 0, 0)

[node name="CameraPivot" type="Node3D" parent="CameraController"]

[node name="Environment" type="Node3D" parent="."]
script = ExtResource("4_environment")

[node name="Ground" type="StaticBody3D" parent="Environment"]

[node name="MeshInstance3D" type="MeshInstance3D" parent="Environment/Ground"]
material_override = SubResource("StandardMaterial3D_1")
mesh = SubResource("BoxMesh_1")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Environment/Ground"]
shape = SubResource("BoxShape3D_1")

[node name="ReferenceCubes" type="Node3D" parent="Environment"]

[node name="RedCube" type="StaticBody3D" parent="Environment/ReferenceCubes"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 10, 0.5, 10)

[node name="MeshInstance3D" type="MeshInstance3D" parent="Environment/ReferenceCubes/RedCube"]
material_override = SubResource("StandardMaterial3D_4")
mesh = SubResource("BoxMesh_3")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Environment/ReferenceCubes/RedCube"]
shape = SubResource("BoxShape3D_2")

[node name="GreenCube" type="StaticBody3D" parent="Environment/ReferenceCubes"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -10, 0.5, 10)

[node name="MeshInstance3D" type="MeshInstance3D" parent="Environment/ReferenceCubes/GreenCube"]
material_override = SubResource("StandardMaterial3D_5")
mesh = SubResource("BoxMesh_3")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Environment/ReferenceCubes/GreenCube"]
shape = SubResource("BoxShape3D_2")

[node name="BlueCube" type="StaticBody3D" parent="Environment/ReferenceCubes"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 10, 0.5, -10)

[node name="MeshInstance3D" type="MeshInstance3D" parent="Environment/ReferenceCubes/BlueCube"]
material_override = SubResource("StandardMaterial3D_6")
mesh = SubResource("BoxMesh_3")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Environment/ReferenceCubes/BlueCube"]
shape = SubResource("BoxShape3D_2")

[node name="YellowCube" type="StaticBody3D" parent="Environment/ReferenceCubes"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -10, 0.5, -10)

[node name="MeshInstance3D" type="MeshInstance3D" parent="Environment/ReferenceCubes/YellowCube"]
material_override = SubResource("StandardMaterial3D_7")
mesh = SubResource("BoxMesh_3")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Environment/ReferenceCubes/YellowCube"]
shape = SubResource("BoxShape3D_2")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.707107, -0.5, 0.5, 0, 0.707107, 0.707107, -0.707107, -0.5, 0.5, 0, 10, 5)
shadow_enabled = true
