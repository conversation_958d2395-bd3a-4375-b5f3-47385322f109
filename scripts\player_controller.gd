extends CharacterBody3D
class_name Player<PERSON>ontroller

# Player states
enum PlayerState {
	WALKING,
	SKATEBOARDING
}

# Movement properties
@export var walk_speed: float = 5.0
@export var run_speed: float = 8.0
@export var jump_velocity: float = 4.5
@export var acceleration: float = 10.0
@export var friction: float = 10.0

# Skateboard properties
@export var skateboard_scene: PackedScene
var skateboard_instance: RigidBody3D
var is_on_skateboard: bool = false
var current_state: PlayerState = PlayerState.WALKING

# Physics
var gravity: float = ProjectSettings.get_setting("physics/3d/default_gravity")

# References
@onready var mesh_instance: MeshInstance3D = $MeshInstance3D
@onready var collision_shape: CollisionShape3D = $CollisionShape3D
@onready var skateboard_pickup_area: Area3D = $SkateboardPickupArea
var camera_controller

# Skateboard interaction
var nearby_skateboard: RigidBody3D = null

# Collision settings storage for skateboard mode
var original_collision_layer: int
var original_collision_mask: int

# Deck surface detection
var is_on_deck_surface: bool = false

signal state_changed(new_state: PlayerState)

func _ready():
	# Store original collision settings
	original_collision_layer = collision_layer
	original_collision_mask = collision_mask

	# Connect skateboard pickup area signals
	if skateboard_pickup_area:
		skateboard_pickup_area.body_entered.connect(_on_skateboard_area_entered)
		skateboard_pickup_area.body_exited.connect(_on_skateboard_area_exited)

	# Connect deck detection area
	var deck_area = $DeckDetectionArea
	deck_area.area_entered.connect(_on_deck_area_entered)
	deck_area.area_exited.connect(_on_deck_area_exited)

	# Create player visual representation
	_create_player_mesh()

func _create_player_mesh():
	# Player mesh and collision will be set up in the scene editor
	# This function can be removed or used for any runtime setup if needed
	pass

func _physics_process(delta):
	match current_state:
		PlayerState.WALKING:
			_handle_walking_physics(delta)
		PlayerState.SKATEBOARDING:
			_handle_skateboard_physics(delta)

func _handle_walking_physics(delta):
	# Add gravity
	if not _is_player_on_floor():
		velocity.y -= gravity * delta

	# Handle jump
	if Input.is_action_just_pressed("ui_accept") and _is_player_on_floor():
		velocity.y = jump_velocity

	# Get input direction
	var input_dir = Vector2.ZERO
	if Input.is_action_pressed("move_forward"):
		input_dir.y += 1
	if Input.is_action_pressed("move_backward"):
		input_dir.y -= 1
	if Input.is_action_pressed("move_left"):
		input_dir.x -= 1
	if Input.is_action_pressed("move_right"):
		input_dir.x += 1

	# Normalize input to prevent faster diagonal movement
	input_dir = input_dir.normalized()

	# Get camera direction for relative movement
	var camera_forward = Vector3.ZERO
	var camera_right = Vector3.ZERO

	if camera_controller:
		# Get camera's forward direction (projected onto horizontal plane)
		var cam_transform = camera_controller.global_transform
		camera_forward = -cam_transform.basis.z
		camera_forward.y = 0  # Remove vertical component
		camera_forward = camera_forward.normalized()

		# Get camera's right direction
		camera_right = cam_transform.basis.x
		camera_right.y = 0  # Remove vertical component
		camera_right = camera_right.normalized()
	else:
		# Fallback to world directions if no camera
		camera_forward = Vector3(0, 0, -1)
		camera_right = Vector3(1, 0, 0)

	# Calculate movement direction relative to camera
	var direction = camera_forward * input_dir.y + camera_right * input_dir.x

	# Determine speed based on run input
	var target_speed = run_speed if Input.is_action_pressed("run") else walk_speed

	if direction != Vector3.ZERO:
		# Move relative to camera direction
		velocity.x = direction.x * target_speed
		velocity.z = direction.z * target_speed

		# Rotate player to face movement direction
		var target_rotation = atan2(direction.x, direction.z)
		rotation.y = lerp_angle(rotation.y, target_rotation, 8.0 * delta)
	else:
		# Stop horizontal movement when no input
		velocity.x = 0
		velocity.z = 0

	move_and_slide()

func _handle_skateboard_physics(delta):
	# When on skateboard, handle simple movement
	if skateboard_instance:
		# Handle skateboard movement input
		_handle_skateboard_movement(delta)

		# Keep player positioned on top of skateboard (feet on deck surface)
		# Get the actual deck surface position from the skateboard
		var deck_surface_area = skateboard_instance.get_node("DeckSurface")
		var deck_collision_shape = deck_surface_area.get_node("CollisionShape3D")
		var deck_surface_y = deck_collision_shape.global_position.y
		# Player feet should be just above the deck surface
		# Account for collision shape offset (-0.1) and half capsule height (0.9)
		var player_y = deck_surface_y + 0.9 + 0.1  # Total offset: 1.0 units above deck surface
		global_position = Vector3(skateboard_instance.global_position.x, player_y, skateboard_instance.global_position.z)

		# Match skateboard rotation for turning
		# Subtract the 90 degree offset to keep player facing correct direction
		rotation.y = skateboard_instance.rotation.y - PI/2

func _handle_skateboard_movement(_delta):
	# Simple skateboard movement similar to walking
	if not skateboard_instance:
		return

	# Get input direction
	var input_dir = Vector2.ZERO
	if Input.is_action_pressed("move_forward"):
		input_dir.y += 1
	if Input.is_action_pressed("move_backward"):
		input_dir.y -= 1
	if Input.is_action_pressed("move_left"):
		input_dir.x -= 1
	if Input.is_action_pressed("move_right"):
		input_dir.x += 1

	# Normalize input to prevent faster diagonal movement
	input_dir = input_dir.normalized()

	# Get camera direction for relative movement
	var camera_forward = Vector3.ZERO
	var camera_right = Vector3.ZERO

	if camera_controller:
		# Get camera's forward direction (projected onto horizontal plane)
		var cam_transform = camera_controller.global_transform
		camera_forward = -cam_transform.basis.z
		camera_forward.y = 0  # Remove vertical component
		camera_forward = camera_forward.normalized()

		# Get camera's right direction
		camera_right = cam_transform.basis.x
		camera_right.y = 0  # Remove vertical component
		camera_right = camera_right.normalized()
	else:
		# Fallback to world directions if no camera
		camera_forward = Vector3(0, 0, -1)
		camera_right = Vector3(1, 0, 0)

	# Calculate movement direction relative to camera
	var direction = camera_forward * input_dir.y + camera_right * input_dir.x

	# Apply movement to skateboard
	if direction != Vector3.ZERO:
		var skateboard_speed = 8.0  # Similar to walking speed
		var movement_force = direction * skateboard_speed

		# Apply the movement as velocity to the skateboard's RigidBody3D
		# Preserve Y velocity (gravity) while setting X and Z movement
		skateboard_instance.linear_velocity.x = movement_force.x
		skateboard_instance.linear_velocity.z = movement_force.z

		# Rotate skateboard to face movement direction
		var target_rotation = atan2(direction.x, direction.z) + PI/2
		skateboard_instance.rotation.y = target_rotation
	else:
		# When not moving, stop horizontal movement but preserve gravity
		skateboard_instance.linear_velocity.x = 0
		skateboard_instance.linear_velocity.z = 0

# Skateboard input handling removed - ready for new system

func _input(event):
	if event.is_action_pressed("interact_skateboard"):
		_toggle_skateboard()

func _toggle_skateboard():
	if current_state == PlayerState.WALKING:
		if nearby_skateboard:
			_get_on_skateboard(nearby_skateboard)
		else:
			_spawn_skateboard()
	else:
		_get_off_skateboard()

func _spawn_skateboard():
	if skateboard_scene:
		skateboard_instance = skateboard_scene.instantiate()
		get_parent().add_child(skateboard_instance)

		# Position skateboard on the ground under player's feet
		# Place skateboard at ground level (y = 0) and let physics handle it
		skateboard_instance.global_position = Vector3(global_position.x, 0.05, global_position.z)

		# Match skateboard rotation to player's current facing direction
		# Add 90 degrees (PI/2) so skateboard front faces forward
		skateboard_instance.rotation.y = rotation.y + PI/2

		_get_on_skateboard(skateboard_instance)
	else:
		push_error("No skateboard scene assigned!")

func _get_on_skateboard(skateboard: RigidBody3D):
	skateboard_instance = skateboard
	is_on_skateboard = true
	current_state = PlayerState.SKATEBOARDING

	# Store current player rotation to preserve facing direction
	var player_rotation = rotation.y

	# Position player on top of skateboard (feet on deck surface)
	# Get the actual deck surface position from the skateboard
	var deck_surface_area = skateboard_instance.get_node("DeckSurface")
	var deck_collision_shape = deck_surface_area.get_node("CollisionShape3D")
	var deck_surface_y = deck_collision_shape.global_position.y

	# Player feet should be just above the deck surface
	# Account for collision shape offset (-0.1) and half capsule height (0.9)
	var player_y = deck_surface_y + 0.9 + 0.1  # Total offset: 1.0 units above deck surface
	global_position = Vector3(skateboard_instance.global_position.x, player_y, skateboard_instance.global_position.z)

	# Preserve player's facing direction (don't change rotation)
	rotation.y = player_rotation

	# Properly disable collision detection when on skateboard
	# Instead of just disabling the CollisionShape3D, we need to change collision layers
	# Disable collision with ground (layer 1) by setting collision_mask to 0
	# This prevents CharacterBody3D from detecting floor collisions
	collision_mask = 0
	collision_layer = 0

	# Connect to skateboard
	if skateboard_instance.has_method("set_player"):
		skateboard_instance.set_player(self)

	state_changed.emit(current_state)

func _get_off_skateboard():
	if skateboard_instance:
		# Keep player in exact same position - no movement when getting off
		# Player should just "plop down" where they are

		# Disconnect from skateboard
		if skateboard_instance.has_method("set_player"):
			skateboard_instance.set_player(null)

		# Remove skateboard from scene (despawn it)
		skateboard_instance.queue_free()
		skateboard_instance = null

	is_on_skateboard = false
	current_state = PlayerState.WALKING

	# Restore original collision settings
	collision_layer = original_collision_layer
	collision_mask = original_collision_mask

	state_changed.emit(current_state)

# Helper function to determine if player should be considered "on floor"
# When on skateboard, we check if standing on deck surface instead of ground
func _is_player_on_floor() -> bool:
	if current_state == PlayerState.SKATEBOARDING:
		return is_on_deck_surface  # When on skateboard, check deck surface
	else:
		return is_on_floor()  # Normal floor detection when walking

func _on_skateboard_area_entered(body):
	if body.is_in_group("skateboard") and not is_on_skateboard:
		nearby_skateboard = body

func _on_skateboard_area_exited(body):
	if body == nearby_skateboard:
		nearby_skateboard = null

func _on_deck_area_entered(area):
	if area.get_parent().is_in_group("skateboard"):
		is_on_deck_surface = true

func _on_deck_area_exited(area):
	if area.get_parent().is_in_group("skateboard"):
		is_on_deck_surface = false

func get_current_state() -> PlayerState:
	return current_state

func is_skateboarding() -> bool:
	return current_state == PlayerState.SKATEBOARDING
